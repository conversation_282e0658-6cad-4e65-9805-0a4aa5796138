"use client"

import Link from "next/link"
import { useParams, usePathname } from "next/navigation"

import { IconDashboard, IconGitBranch, IconGitPullRequest, IconPlug } from "@tabler/icons-react"
import { SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar"

const data = {
    navMain: [
        {
            title: "Dashboard",
            url: "/dashboard",
            icon: IconDashboard,
        },
        {
            title: "Inventory",
            url: "/inventory",
            icon: IconGitBranch,
        },
        {
            title: "Pipelines",
            url: "/pipelines",
            icon: IconGitPullRequest,
        },
        {
            title: "Integrations",
            url: "/integrations",
            icon: IconPlug,
        },
    ]
}


export function NavMain() {
    const pathname = usePathname()
    const userId = useParams().userId

    return (
        <SidebarGroup>
            <SidebarGroupLabel>Platform</SidebarGroupLabel>
            <SidebarGroupContent className="flex flex-col gap-2">
                <SidebarMenu>
                    {
                        data.navMain.map((item) => {
                            const href = `/${userId}${item.url}`
                            const isActive = pathname === href

                            return (
                                <SidebarMenuItem key={item.title}>
                                    <SidebarMenuButton
                                        tooltip={item.title}
                                        asChild
                                        isActive={isActive}
                                    >
                                        <Link href={href}>
                                            {item.icon && <item.icon />}
                                            <span>{item.title}</span>
                                        </Link>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                            )
                        })
                    }
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
    )
}
