import { getAuthenticatedServerClient, handleSupabaseError } from '@/utils/supabase/server/connect'

export class IntegrationService {
    static async getUserIntegrations(userId, githubAppSlug) {
        try {
            const { supabase, userId: authUserId } = await getAuthenticatedServerClient()

            if (authUserId !== userId) {
                throw new Error('Unauthorized access')
            }

            return await supabase.rpc('get_user_integrations', { user_id_param: userId, github_app_slug_param: githubAppSlug })
        } catch (error) {
            handleSupabaseError(error, 'getUserIntegrations')
        }
    }
}
