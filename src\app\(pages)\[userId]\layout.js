import { redirect } from 'next/navigation'
import { currentUser, auth } from '@clerk/nextjs/server'

import { AppSidebar } from "@/components/app-sidebar"
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { DynamicBreadcrumb } from "@/components/dynamic-breadcrumb"

export default async function ProtectedLayoutContent({ children, params }) {
    const { userId: authUserId } = await auth()
    const { userId: routeUserId } = await params

    if (authUserId !== routeUserId) {
        redirect(`/${authUserId}/dashboard`)
    }

    const user = await currentUser()

    if (!user) {
        return <div>Loading...</div>
    }

    return (
        <SidebarProvider>
            <AppSidebar user={user} />
            <SidebarInset>
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                    <div className="flex items-center gap-2 px-4">
                        <SidebarTrigger className="-ml-1" />

                        <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />

                        <DynamicBreadcrumb />
                    </div>
                </header>

                <main className="flex flex-1 flex-col gap-4 p-4 pt-0">
                    {children}
                </main>

            </SidebarInset>
        </SidebarProvider>
    )
}
