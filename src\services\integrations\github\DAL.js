import { getAuthenticatedServerClient, handleSupabaseError } from '@/utils/supabase/server/connect'

export class GitHubIntegrationService {
    static async createGitHubIntegration(userId, githubData) {
        try {
            const { supabase, userId: authUserId } = await getAuthenticatedServerClient()

            if (authUserId !== userId) {
                throw new Error('Unauthorized access')
            }

            return await supabase.rpc('create_github_integration', { user_id_param: userId, github_data_param: githubData })

        } catch (error) {
            handleSupabaseError(error, 'createGitHubIntegration')
        }
    }

    static async getGitHubRepositories(userId) {
        try {
            const { supabase, userId: authUserId } = await getAuthenticatedServerClient()

            if (authUserId !== userId) {
                throw new Error('Unauthorized access')
            }
            return await supabase.rpc('get_github_integration_repositories', { user_id_param: userId })
        } catch (error) {
            handleSupabaseError(error, 'getGitHubRepositories')
        }
    }
}
