import { subscribe } from "@inngest/realtime"
import { inngest } from "@/inngest/client"

export async function GET(request, { params }) {
    const { userId } = params

    try {
        const stream = await subscribe(inngest, {
            channel: `user:${userId}:scans`,
            topics: [
                "repositoryStarted",
                "repositoryCompleted",
                "jobProgress",
                "scan/repository.progress",
                "scan/job.progress",
                "scan/job.completed"
            ]
        })

        return new Response(stream.getEncodedStream(), {
            headers: {
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET",
                "Access-Control-Allow-Headers": "Cache-Control"
            },
        })
    } catch (error) {
        console.error('SSE stream error:', error)
        return new Response('Error setting up stream', { status: 500 })
    }
}
