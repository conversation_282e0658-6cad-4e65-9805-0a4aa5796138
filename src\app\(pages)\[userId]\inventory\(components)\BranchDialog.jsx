"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { Label } from "@/components/ui/label"
import { Loader2, Scan, Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { fetchRepositoryBranches } from "@/services/integrations/github/api"

export function BranchDialog({ selectedRows, isScanning, onScan }) {
    const [repositoryBranches, setRepositoryBranches] = useState({})
    const [selectedBranches, setSelectedBranches] = useState({})
    const [openComboboxes, setOpenComboboxes] = useState({})
    const [loadingBranches, setLoadingBranches] = useState({})
    const [isOpen, setIsOpen] = useState(false)

    const selectedRepos = Array.from(selectedRows)

    useEffect(() => {
        const newSelectedBranches = {}
        selectedRepos.forEach(repo => {
            newSelectedBranches[repo.repository_id] = repo.default_branch
            fetchBranches(repo)
        })
        setSelectedBranches(newSelectedBranches)
    }, [selectedRows])

    const fetchBranches = async (repo) => {
        if (repositoryBranches[repo.repository_id]) return

        setLoadingBranches(prev => ({ ...prev, [repo.repository_id]: true }))
        try {
            const branches = await fetchRepositoryBranches(repo.full_name, repo.installation_id)
            setRepositoryBranches(prev => ({
                ...prev,
                [repo.repository_id]: branches.map(branch => branch.name)
            }))
        } catch (error) {
            console.error('Failed to fetch branches:', error)
            setRepositoryBranches(prev => ({
                ...prev,
                [repo.repository_id]: [selectedBranches[repo.repository_id] || 'main']
            }))
        } finally {
            setLoadingBranches(prev => ({ ...prev, [repo.repository_id]: false }))
        }
    }

    const handleBranchSelect = (repositoryId, branch) => {
        setSelectedBranches(prev => ({
            ...prev,
            [repositoryId]: branch
        }))
        setOpenComboboxes(prev => ({
            ...prev,
            [repositoryId]: false
        }))
    }

    const handleScan = () => {
        setIsOpen(false)

        // Create enriched repository objects with selected branches
        const enrichedRepositories = selectedRepos.map(repo => ({
            ...repo,
            selected_branch: selectedBranches[repo.repository_id] || repo.default_branch
        }))

        onScan(enrichedRepositories)
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" disabled={selectedRows.size === 0 || isScanning}>
                    {isScanning ? (
                        <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Scanning...
                        </>
                    ) : (
                        <>
                            <Scan className="mr-2 h-4 w-4" />
                            Scan ({selectedRows.size})
                        </>
                    )}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>Select Branches to Scan</DialogTitle>
                    <DialogDescription>
                        Choose the branch to scan for each selected repository.
                    </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    {selectedRepos.map((repo) => (
                        <div key={repo.repository_id} className="space-y-2">
                            <Label className="text-sm font-medium">
                                {repo.full_name || repo.repository_id}
                            </Label>
                            <Popover
                                open={openComboboxes[repo.repository_id] || false}
                                onOpenChange={(open) => {
                                    setOpenComboboxes(prev => ({
                                        ...prev,
                                        [repo.repository_id]: open
                                    }))
                                    if (open) {
                                        fetchBranches(repo)
                                    }
                                }}
                            >
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="outline"
                                        role="combobox"
                                        aria-expanded={openComboboxes[repo.repository_id] || false}
                                        className="w-full justify-between"
                                    >
                                        {selectedBranches[repo.repository_id] || "Select branch..."}
                                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-full p-0">
                                    <Command>
                                        <CommandInput placeholder="Search branches..." />
                                        <CommandList>
                                            {loadingBranches[repo.repository_id] ? (
                                                <CommandEmpty>
                                                    <Loader2 className="h-4 w-4 animate-spin" />
                                                    Loading branches...
                                                </CommandEmpty>
                                            ) : (
                                                <>
                                                    <CommandEmpty>No branches found.</CommandEmpty>
                                                    <CommandGroup>
                                                        {(repositoryBranches[repo.repository_id] || []).map((branch) => (
                                                            <CommandItem
                                                                key={branch}
                                                                value={branch}
                                                                onSelect={() => handleBranchSelect(repo.repository_id, branch)}
                                                            >
                                                                <Check
                                                                    className={cn(
                                                                        "mr-2 h-4 w-4",
                                                                        selectedBranches[repo.repository_id] === branch
                                                                            ? "opacity-100"
                                                                            : "opacity-0"
                                                                    )}
                                                                />
                                                                {branch}
                                                            </CommandItem>
                                                        ))}
                                                    </CommandGroup>
                                                </>
                                            )}
                                        </CommandList>
                                    </Command>
                                </PopoverContent>
                            </Popover>
                        </div>
                    ))}
                </div>
                <DialogFooter className="flex justify-between">
                    <DialogClose asChild>
                        <Button variant="outline">Cancel</Button>
                    </DialogClose>
                    <Button onClick={handleScan} disabled={isScanning}>
                        {isScanning ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Scanning...
                            </>
                        ) : (
                            <>
                                <Scan className="mr-2 h-4 w-4" />
                                Start Scan
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
