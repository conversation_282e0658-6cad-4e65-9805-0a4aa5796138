"use client"

import Link from "next/link"
import { useRouter } from 'next/navigation';
import { useState, useMemo, useTransition } from "react"

import { toast } from 'sonner'

import {
    Loader2,
    ChevronDownIcon,
    ChevronUpIcon,
    GitBranchIcon,
    LockIcon,
    UnlockIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
    ChevronsLeftIcon,
    ChevronsRightIcon,
    RefreshCcwIcon,
    SearchIcon,
    SettingsIcon
} from "lucide-react"

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"

import { startRepositoriesScan } from '@/services/scanner'
import { BranchDialog } from "./BranchDialog";
import { cn } from "@/lib/utils"

export default function RepositoriesTable({ repositories, userId }) {
    const [selectedRows, setSelectedRows] = useState(new Set())
    const [sortField, setSortField] = useState(null)
    const [sortDirection, setSortDirection] = useState("asc")
    const [filterText, setFilterText] = useState("")
    const [visibilityFilter, setVisibilityFilter] = useState("all")
    const [providerFilter, setProviderFilter] = useState("all")
    const [currentPage, setCurrentPage] = useState(1)
    const [pageSize, setPageSize] = useState(10)
    const [isScanning, setIsScanning] = useState(false)

    const router = useRouter();
    const [isPending, startTransition] = useTransition();

    const handleReload = () =>
        startTransition(() => {
            router.refresh();
        });


    const handleScan = async (enrichedRepositories) => {
        if (selectedRows.size === 0) {
            toast.error('Please select repositories to scan')
            return
        }
        setIsScanning(true)
        try {
            const result = await startRepositoriesScan(userId, enrichedRepositories)

            console.log('Scan result:', result)

            if (result.success) {
                toast.success(result.message || 'Repository scan started successfully')
            } else {
                toast.error(result.error || 'Failed to start repository scan')
            }
        } catch (error) {
            console.error('Scan error:', error)
            toast.error('An unexpected error occurred while starting the scan')
        } finally {
            setIsScanning(false)
            setSelectedRows(new Set())
        }
    }

    const uniqueProviders = useMemo(() => {
        const providers = repositories
            .map(repo => repo.provider)
            .filter(Boolean)
            .filter((provider, index, arr) => arr.indexOf(provider) === index)
            .sort()
        return providers
    }, [repositories])

    // Filter repositories
    const filteredRepositories = useMemo(() => {
        return repositories.filter(repo => {
            const matchesText = !filterText ||
                repo.name.toLowerCase().includes(filterText.toLowerCase()) ||
                repo.full_name.toLowerCase().includes(filterText.toLowerCase()) ||
                (repo.description && repo.description.toLowerCase().includes(filterText.toLowerCase()))

            const matchesVisibility = visibilityFilter === "all" ||
                (visibilityFilter === "private" && repo.is_private) ||
                (visibilityFilter === "public" && !repo.is_private)

            const matchesProvider = providerFilter === "all" ||
                repo.provider === providerFilter

            return matchesText && matchesVisibility && matchesProvider
        })
    }, [repositories, filterText, visibilityFilter, providerFilter])

    // Sort repositories
    const sortedRepositories = useMemo(() => {
        if (sortField === null) {
            return [...filteredRepositories] // Return unsorted if no sort field is selected
        }

        return [...filteredRepositories].sort((a, b) => {
            let aValue = a[sortField]
            let bValue = b[sortField]

            // Handle null/undefined values
            if (aValue === null || aValue === undefined) aValue = ""
            if (bValue === null || bValue === undefined) bValue = ""

            // Handle different data types
            if (typeof aValue === "string") {
                aValue = aValue.toLowerCase()
                bValue = bValue.toLowerCase()
            }

            if (sortDirection === "asc") {
                return aValue > bValue ? 1 : -1
            } else {
                return aValue < bValue ? 1 : -1
            }
        })
    }, [filteredRepositories, sortField, sortDirection])

    // Paginate repositories
    const paginatedRepositories = useMemo(() => {
        const startIndex = (currentPage - 1) * pageSize
        return sortedRepositories.slice(startIndex, startIndex + pageSize)
    }, [sortedRepositories, currentPage, pageSize])

    const totalPages = Math.ceil(sortedRepositories.length / pageSize)

    // Handle sorting
    const handleSort = (field) => {
        if (sortField === field) {
            setSortDirection(sortDirection === "asc" ? "desc" : "asc")
        } else {
            setSortField(field)
            setSortDirection("asc")
        }
    }

    // Handle row selection
    const handleRowSelect = (repository) => {
        const newSelected = new Set(selectedRows)
        const existingRepo = Array.from(newSelected).find(repo => repo.repository_id === repository.repository_id)

        if (existingRepo) {
            newSelected.delete(existingRepo)
        } else {
            newSelected.add({
                repository_id: repository.repository_id,
                full_name: repository.full_name,
                default_branch: repository.default_branch,
                installation_id: repository.installation_id,
                repository_name: repository.name
            })
        }
        setSelectedRows(newSelected)
    }

    const handleSelectAll = () => {
        // Filter out repositories that are currently being scanned
        const selectableRepositories = paginatedRepositories.filter(repo => !repo.is_scanning)

        // Check if all selectable repositories are already selected
        const allSelectableSelected = selectableRepositories.every(repo =>
            Array.from(selectedRows).some(selected => selected.repository_id === repo.repository_id)
        )

        if (allSelectableSelected && selectedRows.size > 0) {
            // Deselect all
            setSelectedRows(new Set())
        } else {
            // Select all selectable repositories
            setSelectedRows(new Set(selectableRepositories.map(repo => ({
                repository_id: repo.repository_id,
                full_name: repo.full_name,
                default_branch: repo.default_branch,
                installation_id: repo.installation_id,
                repository_name: repo.repository_name
            }))))
        }
    }

    // Helper function to check if a repository is selected
    const isRepositorySelected = (repositoryId) => {
        return Array.from(selectedRows).some(repo => repo.repository_id === repositoryId)
    }

    // Sort indicator component
    const SortIndicator = ({ field }) => {
        if (sortField !== field || sortField === null) {
            return <div className="ml-1 h-4 w-4" /> // Invisible placeholder to maintain space
        }
        return sortDirection === "asc" ?
            <ChevronUpIcon className="ml-1 h-4 w-4" /> :
            <ChevronDownIcon className="ml-1 h-4 w-4" />
    }

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return "N/A"
        return new Date(dateString).toLocaleDateString()
    }

    return (
        <div className="space-y-4">
            {/* Header with stats */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold">Repository Inventory</h1>
                    <p className="text-muted-foreground">
                        {selectedRows.size > 0 && `${selectedRows.size} selected • `}
                        {sortedRepositories.length} of {repositories.length} repositories
                    </p>
                </div>
            </div>

            {/* Filters */}
            <Card>
                <CardContent>
                    <div className="flex flex-wrap gap-4">
                        <div className="flex items-center gap-2">
                            <SearchIcon className="h-4 w-4" />
                            <span>Search</span>
                        </div>
                        <div className="flex-1 min-w-[200px]">
                            <Input
                                placeholder="Search repositories..."
                                value={filterText}
                                onChange={(e) => setFilterText(e.target.value)}
                                className="w-full"
                            />
                        </div>
                        <Select value={visibilityFilter} onValueChange={setVisibilityFilter}>
                            <SelectTrigger className="w-[150px]">
                                <SelectValue placeholder="Visibility" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Visibility</SelectItem>
                                <SelectItem value="public">Public</SelectItem>
                                <SelectItem value="private">Private</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={providerFilter} onValueChange={setProviderFilter}>
                            <SelectTrigger className="w-[150px]">
                                <SelectValue placeholder="Provider" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Providers</SelectItem>
                                {uniqueProviders.map(provider => (
                                    <SelectItem key={provider} value={provider}>{provider}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <div className="flex items-center px-4 h-8 justify-center">|</div>

                        <BranchDialog selectedRows={selectedRows} isScanning={isScanning} onScan={handleScan} />

                        <Button
                            variant="outline"
                            className="cursor-pointer"
                            onClick={handleReload}
                            disabled={isPending}
                        >
                            {isPending ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Reloading…
                                </>
                            ) : (
                                <>
                                    <RefreshCcwIcon className="mr-2 h-4 w-4" />
                                    Reload
                                </>
                            )}
                        </Button>

                        {/* <Link href={`/${userId}/integrations`}> */}
                        <Button variant="outline" className="cursor-pointer" >
                            <SettingsIcon className="h-4 w-4" />
                            Configuration
                        </Button>
                        {/* </Link> */}
                    </div>
                </CardContent>
            </Card>

            {/* Table */}
            <Card>
                <CardContent className="p-0">
                    <Table className="table-fixed w-full">
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[3%] pl-4">
                                    <Checkbox
                                        checked={selectedRows.size === paginatedRepositories.length && paginatedRepositories.length > 0}
                                        onCheckedChange={handleSelectAll}
                                    />
                                </TableHead>
                                <TableHead className="w-[17%] p-0">
                                    <div
                                        onClick={() => handleSort("name")}
                                        className="flex items-center w-full h-full p-2 font-semibold cursor-pointer hover:bg-muted/50 transition-colors"
                                    >
                                        <span>Repository</span>
                                        <SortIndicator field="name" />
                                    </div>
                                </TableHead>
                                <TableHead className="w-[10%] p-0">
                                    <div
                                        onClick={() => handleSort("provider")}
                                        className="flex items-center w-full h-full p-2 font-semibold cursor-pointer hover:bg-muted/50 transition-colors"
                                    >
                                        <span>Provider</span>
                                        <SortIndicator field="provider" />
                                    </div>
                                </TableHead>
                                {/* <TableHead className="w-[15%] p-0">
                                    <div
                                        onClick={() => handleSort("updated_at")}
                                        className="flex items-center w-full h-full p-2 font-semibold cursor-pointer hover:bg-muted/50 transition-colors"
                                    >
                                        <span>Updated</span>
                                        <SortIndicator field="updated_at" />
                                    </div>
                                </TableHead> */}
                                <TableHead className="w-[30%] p-0">
                                    <div
                                        onClick={() => handleSort("stargazers_count")}
                                        className="flex items-center w-full h-full p-2 font-semibold cursor-pointer hover:bg-muted/50 transition-colors"
                                    >
                                        <span>Last Scan Status</span>
                                        <SortIndicator field="stargazers_count" />
                                    </div>
                                </TableHead>
                                <TableHead className="w-[40%]">
                                    Last Scan Statistics
                                </TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {isPending ? (
                                // Render 5 skeleton rows with 6 columns each
                                Array.from({ length: 5 }).map((_, rowIdx) => (
                                    <TableRow key={"skeleton-" + rowIdx}>
                                        {Array.from({ length: 6 }).map((_, colIdx) => (
                                            <TableCell key={colIdx}>
                                                <Skeleton className="h-6 w-full" />
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                paginatedRepositories.map((repo) => {
                                    const isSelected = isRepositorySelected(repo.repository_id)

                                    return (
                                        <TableRow
                                            key={repo.repository_id}
                                            className={cn(
                                                isSelected ? "bg-muted/50" : "",
                                                repo.is_scanning && "opacity-60 pointer-events-none"
                                            )}
                                        >
                                            <TableCell className="pl-4">
                                                <Checkbox
                                                    checked={isSelected}
                                                    disabled={repo.is_scanning}
                                                    onCheckedChange={() => handleRowSelect(repo)}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <div className="space-y-1">
                                                    <div className="flex items-center gap-2">
                                                        <Link href={repo.html_url} target="_blank" rel="noopener noreferrer" className="font-medium hover:underline">
                                                            {repo.name}
                                                        </Link>
                                                        {repo.is_private ? (
                                                            <LockIcon className="h-4 w-4  text-green-500 hover:text-green-500/80" />
                                                        ) : (
                                                            <UnlockIcon className="h-4 w-4  text-red-500 hover:text-red-500/80" />
                                                        )}
                                                    </div>
                                                    <div className="text-sm text-muted-foreground">
                                                        {repo.full_name}
                                                    </div>
                                                    {repo.description && (
                                                        <div className="text-sm text-muted-foreground max-w-[300px] truncate">
                                                            {repo.description}
                                                        </div>
                                                    )}
                                                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                                        {/* Default Branch: */}
                                                        <GitBranchIcon className="h-3 w-3" />
                                                        <p className="font-bold">
                                                            {repo.default_branch || "main"}
                                                        </p>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {
                                                    repo.provider === "github" ? (
                                                        <Badge variant="secondary">{repo.provider}</Badge>
                                                    ) : (
                                                        <span className="text-muted-foreground">GitHub</span>
                                                    )
                                                }
                                            </TableCell>
                                            {/* <TableCell>
                                                <span className="text-sm">
                                                    {formatDate(repo.updated_at)}
                                                </span>
                                            </TableCell> */}
                                            <TableCell>
                                                {repo.is_scanning ? (
                                                    <div className="flex items-center gap-2">
                                                        <Loader2 className="h-4 w-4 animate-spin" />
                                                        <Badge variant="secondary">
                                                            {repo.state === 'pending' ? 'Pending' : 'Scanning'}
                                                        </Badge>
                                                    </div>
                                                ) : (
                                                    <div className="space-y-1">
                                                        <p className="text-sm">
                                                            {repo.last_scan_ended_at ? formatDate(repo.last_scan_ended_at) : "NOT SCANNED YET"}
                                                        </p>
                                                        {repo.last_scan_status && (
                                                            <p className="text-sm text-muted-foreground">
                                                                {repo.last_scan_status}
                                                            </p>
                                                        )}
                                                        {repo.last_scan_message && (
                                                            <p className="text-xs text-muted-foreground">
                                                                {repo.last_scan_message}
                                                            </p>
                                                        )}
                                                    </div>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                Last Scan Stats
                                            </TableCell>
                                        </TableRow>
                                    )
                                })
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            {/* Pagination */}
            <div className="flex items-center justify-between px-4">
                <div className="text-muted-foreground hidden flex-1 text-sm lg:flex">
                    {selectedRows.size} of {sortedRepositories.length} row(s) selected.
                </div>
                <div className="flex w-full items-center gap-8 lg:w-fit">
                    <div className="hidden items-center gap-2 lg:flex">
                        <Label htmlFor="rows-per-page" className="text-sm font-medium">
                            Rows per page
                        </Label>
                        <Select
                            value={`${pageSize}`}
                            onValueChange={(value) => {
                                setPageSize(Number(value))
                                setCurrentPage(1) // Reset to first page when changing page size
                            }}
                        >
                            <SelectTrigger size="sm" className="w-20" id="rows-per-page">
                                <SelectValue placeholder={pageSize} />
                            </SelectTrigger>
                            <SelectContent side="top">
                                {[10, 20, 30, 40, 50].map((size) => (
                                    <SelectItem key={size} value={`${size}`}>
                                        {size}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="flex w-fit items-center justify-center text-sm font-medium">
                        Page {currentPage} of {totalPages}
                    </div>
                    <div className="ml-auto flex items-center gap-2 lg:ml-0">
                        <Button
                            variant="outline"
                            className="hidden h-8 w-8 p-0 lg:flex"
                            onClick={() => setCurrentPage(1)}
                            disabled={currentPage === 1}
                        >
                            <span className="sr-only">Go to first page</span>
                            <ChevronsLeftIcon className="h-4 w-4" />
                        </Button>
                        <Button
                            variant="outline"
                            className="size-8"
                            size="icon"
                            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                            disabled={currentPage === 1}
                        >
                            <span className="sr-only">Go to previous page</span>
                            <ChevronLeftIcon className="h-4 w-4" />
                        </Button>
                        <Button
                            variant="outline"
                            className="size-8"
                            size="icon"
                            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                            disabled={currentPage === totalPages}
                        >
                            <span className="sr-only">Go to next page</span>
                            <ChevronRightIcon className="h-4 w-4" />
                        </Button>
                        <Button
                            variant="outline"
                            className="hidden size-8 lg:flex"
                            size="icon"
                            onClick={() => setCurrentPage(totalPages)}
                            disabled={currentPage === totalPages}
                        >
                            <span className="sr-only">Go to last page</span>
                            <ChevronsRightIcon className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}
