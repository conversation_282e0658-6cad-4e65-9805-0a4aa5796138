import { AlertCircleIcon } from "lucide-react"

import RepositoriesTable from "./(components)/RepositoriesTable"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert"
import { GitHubIntegrationService } from "@/services/integrations/github/DAL"


export default async function InventoryPage({ params }) {
    const { userId } = await params

    const { data: repositories, error } = await GitHubIntegrationService.getGitHubRepositories(userId)

    if (error) {
        return (
            <Alert variant="destructive">
                <AlertCircleIcon className="h-4 w-4" />
                <AlertTitle>Unable to load repositories.</AlertTitle>
                <AlertDescription>
                    <p>{error.message}</p>
                    <p>Please try again later.</p>
                </AlertDescription>
            </Alert>
        )
    }

    if (!repositories || repositories.length === 0) {
        return (
            <Alert>
                <AlertCircleIcon className="h-4 w-4" />
                <AlertTitle>No repositories found.</AlertTitle>
                <AlertDescription>
                    <p>Connect your GitHub account to see your repositories here.</p>
                </AlertDescription>
            </Alert>
        )
    }
    return <RepositoriesTable repositories={repositories} userId={userId} />
}
