import { createClient as createServerClient } from './server'
import { auth } from '@clerk/nextjs/server'
import { cookies } from 'next/headers'

export async function getAuthenticatedServerClient() {
    const { userId } = await auth()
    if (!userId) throw new Error('Unauthorized')

    const cookieStore = await cookies()
    const supabase = createServerClient(cookieStore)

    return { supabase, userId }
}


// Alternative: Service role client for administrative operations
export function getServiceRoleClient() {
    const { createClient } = require('@supabase/supabase-js')

    const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY, // Service role key for admin operations
        {
            auth: {
                autoRefreshToken: false,
                persistSession: false
            }
        }
    )

    return { supabase }
}

export function handleSupabaseError(error, context = '') {
    console.error(`Supabase error in ${context}:`, error)
    throw new Error(error.message || 'Database operation failed')
}
