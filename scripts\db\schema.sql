-- DROP TYPE public.alert_severity;
CREATE TYPE public.alert_severity AS ENUM (
	'critical',
	'high',
	'medium',
	'low',
	'info'
);


-- DROP TYPE public.git_provider_type;
CREATE TYPE public.git_provider_type AS ENUM (
	'github',
	'gitlab',
	'bitbucket'
);


-- DROP TYPE public.rule_created_by_type;
CREATE TYPE public.rule_created_by_type AS ENUM (
	'Puaro Security',
	'Custom'
);


-- DROP TYPE public.scan_target_type;
CREATE TYPE public.scan_target_type AS ENUM (
	'repository',
	'pull_request',
	'push'
);


-- DROP TYPE public.scan_job_state_type;
CREATE TYPE public.scan_job_state_type AS ENUM (
	'running',
	'finished'
);



-- DROP TABLE public.github_integrations;
CREATE TABLE public.github_integrations (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	user_id text NOT NULL,
	installation_id int8 NULL,
	app_slug text NOT NULL,
	installation_target_type text NOT NULL,
	access_tokens_url text NOT NULL,
	repositories_url text NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	is_suspended bool DEFAULT false NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT ukey_github_integrations_user_id_installation_id UNIQUE (user_id, installation_id),
	CONSTRAINT pkey_github_integrations_id PRIMARY KEY (id)
);



-- DROP TABLE public.github_integrations_repositories;
CREATE TABLE public.github_integrations_repositories (
	repository_id int8 NOT NULL,
	github_integration_id uuid NOT NULL,
	installation_id int8 NOT NULL,
	"name" text NOT NULL,
	full_name text NOT NULL,
	default_branch text NULL,
	topics jsonb DEFAULT '[]'::jsonb NULL,
	html_url text NULL,
	branches_url text NULL,
	commits_url text NULL,
	tags_url text NULL,
	clone_url text NULL,
	"size" int8 NULL,
	is_private bool DEFAULT false NULL,
	is_deleted bool DEFAULT false NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT ukey_github_integrations_repositories_repository_id_github_integration_id UNIQUE (repository_id, github_integration_id),
	CONSTRAINT pkey_github_integrations_repositories_repository_id PRIMARY KEY (repository_id)
);



-- DROP TABLE public.scan_jobs;
CREATE TABLE public.scan_jobs (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	scan_target public.scan_target_type NOT NULL,
	git_provider public.git_provider_type NOT NULL,
	executed_by_user_id text NOT NULL,
	state scan_job_state_type NOT NULL,
	is_success boolean NULL,
	message text NULL,
	started_at timestamptz NOT NULL,
	ended_at timestamptz NULL,
	CONSTRAINT pkey_scan_jobs_id PRIMARY KEY (id)
);



-- DROP TABLE public.scan_job_details;
CREATE TABLE public.scan_job_details (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	scan_job_id uuid NOT NULL,
	repository_id uuid DEFAULT gen_random_uuid() NULL,
	target_metadata jsonb DEFAULT '{}'::jsonb NOT NULL,

	total_lines_scanned int4 DEFAULT 0 NULL,
	critical_alerts_found int4 DEFAULT 0 NULL,
	high_alerts_found int4 DEFAULT 0 NULL,
	medium_alerts_found int4 DEFAULT 0 NULL,
	low_alerts_found int4 DEFAULT 0 NULL,
	none_alerts_found int4 DEFAULT 0 NULL,

	created_at timestamptz DEFAULT now() NOT NULL,

	CONSTRAINT pkey_scan_job_details_id PRIMARY KEY (id)
);



-- DROP TABLE public.scanned_commits;
CREATE TABLE public.scanned_commits (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	scan_job_id uuid NOT NULL,
	commit_hash text NOT NULL,
	pr_id int8 NOT NULL,
	repository_id int8 NOT NULL,
	scanned_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT pkey_scanned_commits_commit_hash PRIMARY KEY (commit_hash)
);



-- DROP TABLE public.scan_alerts;
CREATE TABLE public.scan_alerts (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	scan_job_detail_id uuid NOT NULL,

	rule_id text NOT NULL,
	title text NOT NULL,
	description text NULL,
	rule_created_by public.rule_created_by_type NULL,

	severity public.alert_severity NOT NULL,
	category text NULL,
	confidence float8 NULL,

	file_path text NOT NULL,
	line_number int4 NULL,
	code_snippet text NULL,
	context_url text NULL,
	masked_secret text NULL,
	secret_type text NULL,

	is_false_positive bool DEFAULT false NULL,
	is_resolved bool DEFAULT false NULL,
	resolved_at timestamptz NULL,
	resolved_by text NULL,
	resolution_notes text NULL,

	fingerprint text NOT NULL,

	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT pkey_scan_alerts_id PRIMARY KEY (id)
);



-- DROP TABLE public.gitlab_integrations;
CREATE TABLE public.gitlab_integrations (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	user_id text NOT NULL,
	integration_details json NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT pkey_gitlab_integrations_id PRIMARY KEY (id)
);



-- DROP TABLE public.slack_integrations;
CREATE TABLE public.slack_integrations (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	user_id text NOT NULL,
	integration_details json NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT pkey_slack_integrations_id PRIMARY KEY (id)
);
