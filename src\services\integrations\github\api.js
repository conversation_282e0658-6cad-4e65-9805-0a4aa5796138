import { generateGitHubAppJWT, getInstallationAccessToken } from './auth'

export async function fetchInstallationDetails(installationId) {
    const jwt = await generateGitHubAppJWT()

    const response = await fetch(`https://api.github.com/app/installations/${installationId}`, {
        headers: {
            'Authorization': `Bearer ${jwt}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'your-app-name'
        }
    })
    if (!response.ok) {
        throw new Error('Failed to fetch GitHub installation')
    }

    return await response.json()
}


export async function fetchInstallationRepositories(installationId) {
    const installationToken = await getInstallationAccessToken(installationId)

    const response = await fetch(`https://api.github.com/installation/repositories`, {
        headers: {
            'Authorization': `token ${installationToken}`,
            'Accept': 'application/vnd.github.v3+json'
        }
    })

    if (!response.ok) {
        throw new Error('Failed to fetch installation repositories')
    }

    const data = await response.json()
    return data.repositories || []
}


export async function fetchRepositoryBranches(repositoryFullName, installationId) {
    const installationToken = await getInstallationAccessToken(installationId)

    try {
        const response = await fetch(`https://api.github.com/repos/${repositoryFullName}/branches`, {
            headers: {
                'Authorization': `token ${installationToken}`,
                'Accept': 'application/vnd.github.v3+json'
            }
        })
        const data = await response.json()
        return data || []
    } catch (error) {
        console.error('Failed to fetch repository branches:', error)
        throw error
    }
}
