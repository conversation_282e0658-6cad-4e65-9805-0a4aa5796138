'use server'

import { getAuthenticatedServerClient, handleSupabaseError } from '@/utils/supabase/server/connect'
import { revalidatePath } from 'next/cache'
import { inngest } from '@/inngest/client'

export async function startRepositoriesScan(userId, enrichedRepositories) {
    try {
        const { supabase, userId: authUserId } = await getAuthenticatedServerClient()

        if (authUserId !== userId) {
            throw new Error('Unauthorized access')
        }

        // Step 1: Create scan job in database
        const { data: jobId, error } = await supabase.rpc('start_repositories_scan', {
            scan_target_param: 'repository',
            git_provider_param: 'github',
            user_id_param: userId,
            state_param: 'pending',
            target_metadata_param: enrichedRepositories
        })

        if (error) {
            throw new Error(`Failed to create scan job: ${error.message}`)
        }

        // Step 2: Trigger Inngest job (triggers parent function)
        await inngest.send({
            name: 'scan/repositories.requested',
            data: {
                jobId,
                repositories: enrichedRepositories
            }
        })

        // Revalidate the inventory page to show updated data
        revalidatePath(`/${userId}/inventory`)

        return {
            success: true,
            jobId: jobId,
            message: 'Repository scan started successfully'
        }
    } catch (error) {
        console.error('Scanner service error:', error)
        handleSupabaseError(error, 'startRepositoriesScan')

        return {
            success: false,
            error: error.message || 'Failed to start repository scan'
        }
    }
}
