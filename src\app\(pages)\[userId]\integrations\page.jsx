import { IntegrationService } from "@/services/integrations/shared/DAL"

import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardHeader,
    CardTitle,
} from "@/components/ui/card"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { GitHubIcon, GitLabIcon, BitbucketIcon, SlackIcon } from "@/components/icons"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert"
import { AlertCircleIcon } from "lucide-react"

const iconMap = {
    'IconBrandGitlab': GitLabIcon,
    'IconBrandSlack': SlackIcon,
    'IconBrandGithub': GitHubIcon,
    'IconBitbucket': BitbucketIcon,
}

const getIconComponent = (iconName) => {
    return iconMap[iconName] || null
}

export default async function IntegrationsPage({ params }) {
    const { userId } = await params

    const githubAppSlug = process.env.GITHUB_APP_SLUG
    const { data: integrations, error } = await IntegrationService.getUserIntegrations(userId, githubAppSlug)

    if (error) {
        return (
            <Alert variant="destructive">
                <AlertCircleIcon />
                <AlertTitle>Unable to load integrations.</AlertTitle>
                <AlertDescription>
                    <p>Please try again later.</p>
                </AlertDescription>
            </Alert>
        )
    }

    return (
        <div>
            {
                integrations.map((integration) => (
                    <div className="mb-8" key={integration.category}>
                        <div className="mb-4">
                            <h2 className="text-2xl font-bold">
                                {integration.category}
                            </h2>
                            <p className="text-sm text-muted-foreground">
                                {integration.category_description}
                            </p>
                        </div>
                        {
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {
                                    integration.integrations.map((integration) => (
                                        <Card key={integration.integration_name}>
                                            <CardHeader>
                                                <CardTitle>
                                                    <div className="flex items-center gap-4">
                                                        {
                                                            integration.icon && (() => {
                                                                const IconComponent = getIconComponent(integration.icon)
                                                                return IconComponent ? <IconComponent /> : null
                                                            })()
                                                        }
                                                        {integration.integration_name}
                                                    </div>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardFooter className="flex-col">
                                                <Link href={integration?.app_url} className="w-full" target="_blank">
                                                    <Button className="w-full" disabled={integration.is_available !== 'Available'}>
                                                        {integration.is_available === 'Available' ? integration.status : integration.is_available}
                                                    </Button>
                                                </Link>
                                            </CardFooter>
                                        </Card>
                                    ))
                                }
                            </div>
                        }
                    </div>
                ))
            }
        </div>
    )
}
