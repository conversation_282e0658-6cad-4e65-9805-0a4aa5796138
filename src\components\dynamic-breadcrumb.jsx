"use client"

import { usePathname, useParams } from 'next/navigation'
import {
    B<PERSON><PERSON>rumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

// Simple route mapping
const routeNames = {
    dashboard: "Dashboard",
    inventory: "Inventory",
    pipelines: "Pipelines",
    integrations: "Integrations"
}

export function DynamicBreadcrumb() {
    const pathname = usePathname()
    const params = useParams()
    const userId = params.userId

    // Generate breadcrumbs from pathname
    const generateBreadcrumbs = () => {
        const segments = pathname.split('/').filter(Boolean)
        const breadcrumbs = []

        // Skip userId segment, start from index 1
        for (let i = 1; i < segments.length; i++) {
            const segment = segments[i]
            const isLast = i === segments.length - 1
            const href = `/${segments.slice(0, i + 1).join('/')}`

            // Get display name from mapping or capitalize segment
            const title = routeNames[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)

            breadcrumbs.push({
                title,
                href,
                isLast
            })
        }

        return breadcrumbs
    }

    const breadcrumbs = generateBreadcrumbs()

    // Don't show breadcrumb if only on dashboard
    if (breadcrumbs.length === 0 || (breadcrumbs.length === 1 && breadcrumbs[0].title === "Dashboard")) {
        return (
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem>
                        <BreadcrumbPage>Dashboard</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        )
    }

    return (
        <Breadcrumb>
            <BreadcrumbList>
                {breadcrumbs.map((crumb, index) => (
                    <div key={crumb.href} className="flex items-center">
                        <BreadcrumbItem>
                            {crumb.isLast ? (
                                <BreadcrumbPage>{crumb.title}</BreadcrumbPage>
                            ) : (
                                <BreadcrumbLink href={crumb.href}>
                                    {crumb.title}
                                </BreadcrumbLink>
                            )}
                        </BreadcrumbItem>
                        {!crumb.isLast && <BreadcrumbSeparator />}
                    </div>
                ))}
            </BreadcrumbList>
        </Breadcrumb>
    )
}
