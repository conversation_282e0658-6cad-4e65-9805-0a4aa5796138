import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

import { GitHubIntegrationService } from '@/services/integrations/github/DAL'
import { fetchInstallationDetails, fetchInstallationRepositories } from '@/services/integrations/github/api'


export async function GET(request) {
    try {
        const { userId } = await auth()
        if (!userId) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
        }

        const { searchParams } = new URL(request.url)
        const installationId = searchParams.get('installation_id')
        const setupAction = searchParams.get('setup_action')
        const code = searchParams.get('code')

        if (!installationId) {
            return NextResponse.json({ error: 'Missing installation_id' }, { status: 400 })
        }

        if (!code) {
            return NextResponse.json({ error: 'Missing code' }, { status: 400 })
        }

        if (setupAction === 'install') {
            const installationDetails = await fetchInstallationDetails(installationId)
            const repositories = await fetchInstallationRepositories(installationId)

            const githubData = {
                installation_details: installationDetails,
                repositories: repositories
            }

            const { data, error } = await GitHubIntegrationService.createGitHubIntegration(userId, githubData)

            if (error) {
                throw new Error(error.message)
            }

            const baseUrl = process.env.NEXT_PUBLIC_APP_URL || new URL(request.url).origin
            return NextResponse.redirect(new URL(`/${userId}/integrations`, baseUrl))
        }

        return NextResponse.json({ error: 'Invalid setup action' }, { status: 400 })
    } catch (error) {
        console.error('GitHub callback error:', error)
        return NextResponse.json({ error: 'Integration failed' }, { status: 500 })
    }
}
