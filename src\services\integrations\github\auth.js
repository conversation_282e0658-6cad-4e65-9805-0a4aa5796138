'use server'

import jwt from 'jsonwebtoken'
import crypto from 'crypto'

export async function generateGitHubAppJWT() {
    try {
        const GITHUB_PRIVATE_KEY = process.env.GITHUB_PRIVATE_KEY;
        const GITHUB_APP_ID = process.env.GITHUB_APP_ID;

        const now = Math.floor(Date.now() / 1000);

        return jwt.sign({ iat: now, exp: now + (10 * 10), iss: GITHUB_APP_ID }, GITHUB_PRIVATE_KEY, { algorithm: "RS256" });

    } catch (error) {
        return {
            isSuccess: false,
            message: error,
            data: null
        }
    }
}

export async function getInstallationAccessToken(installationId) {
    const jwt = await generateGitHubAppJWT()
    const response = await fetch(`https://api.github.com/app/installations/${installationId}/access_tokens`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${jwt}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'your-app-name'
        }
    })

    if (!response.ok) {
        throw new Error('Failed to get installation access token')
    }

    const { token } = await response.json()
    return token || 'token'
}


export async function verifyGitHubSignature(payload, signature) {
    if (!signature || !process.env.GITHUB_SECRET) {
        return false
    }

    const expectedSignature = 'sha256=' + crypto
        .createHmac('sha256', process.env.GITHUB_SECRET)
        .update(payload, 'utf8')
        .digest('hex')

    return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
    )
}
