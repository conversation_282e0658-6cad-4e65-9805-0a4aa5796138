import { createServerClient } from "@supabase/ssr";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

export const createClient = (cookieStore) => {
    // Validate required environment variables
    if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing required Supabase environment variables')
    }

    if (!cookieStore) {
        throw new Error('Cookie store is required for server-side Supabase client')
    }

    try {
        return createServerClient(
            supabaseUrl,
            supabaseKey,
            {
                cookies: {
                    getAll() {
                        try {
                            return cookieStore.getAll()
                        } catch (error) {
                            console.error('Failed to get cookies:', error)
                            return []
                        }
                    },
                    setAll(cookiesToSet) {
                        try {
                            cookiesToSet.forEach(({ name, value, options }) => {
                                cookieStore.set(name, value, options)
                            })
                        } catch (error) {
                            // The `setAll` method was called from a Server Component.
                            // This can be ignored if you have middleware refreshing user sessions.
                            // Only log in development to avoid production noise
                            if (process.env.NODE_ENV === 'development') {
                                console.warn('Cookie setting failed in Server Component (expected behavior):', error.message)
                            }
                        }
                    },
                },
            },
        )
    } catch (error) {
        console.error('Failed to create Supabase server client:', error)
        throw new Error('Supabase client initialization failed')
    }
};
