import { NextResponse } from 'next/server'
import { headers } from 'next/headers'

import { GitHubWebhookHandler } from '@/services/integrations/github/handlers'
import { verifyGitHubSignature } from '@/services/integrations/github/auth'

export async function POST(request) {
    try {
        const body = await request.text()
        const headersList = await headers()

        const signature = headersList.get('x-hub-signature-256')
        const event = headersList.get('x-github-event')
        const delivery = headersList.get('x-github-delivery')

        if (!signature) {
            console.error('Missing GitHub webhook signature')
            return NextResponse.json({ error: 'Missing signature' }, { status: 401 })
        }

        if (!event) {
            console.error('Missing GitHub event type')
            return NextResponse.json({ error: 'Missing event type' }, { status: 400 })
        }

        if (!process.env.GITHUB_SECRET) {
            console.error('GITHUB_SECRET environment variable not set')
            return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 })
        }

        if (!verifyGitHubSignature(body, signature)) {
            console.error('Invalid GitHub webhook signature')
            return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
        }

        const payload = JSON.parse(body)

        const result = await GitHubWebhookHandler.handleEvent(event, payload)

        return NextResponse.json({
            success: true,
            event,
            delivery,
            processed: result.processed,
            message: result.message || 'Event processed successfully'
        })

    } catch (error) {
        console.error('GitHub webhook error:', error)
        console.error('Error stack:', error.stack)

        return NextResponse.json({
            error: 'Webhook processing failed',
            message: error.message
        }, { status: 500 })
    }
}

// Only allow POST requests
export async function GET() {
    console.log('=== GitHub Webhook GET Request Received ===')
    console.log('This endpoint only accepts POST requests')

    return NextResponse.json({
        error: 'Method not allowed',
        message: 'This webhook endpoint only accepts POST requests',
        allowedMethods: ['POST']
    }, { status: 405 })
}

// Add OPTIONS for CORS preflight
export async function OPTIONS() {
    return NextResponse.json({}, {
        status: 200,
        headers: {
            'Allow': 'POST',
            'Access-Control-Allow-Methods': 'POST',
            'Access-Control-Allow-Headers': 'Content-Type, X-GitHub-Event, X-GitHub-Delivery, X-Hub-Signature-256'
        }
    })
}
