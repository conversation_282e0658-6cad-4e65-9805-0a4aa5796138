import { getServiceRoleClient } from '@/utils/supabase/server/connect'

export class InstallationEventHandler {
    constructor() {
        // Create the service role client once when the class is instantiated
        const { supabase } = getServiceRoleClient()
        this.supabase = supabase
    }

    // Singleton instance
    static instance = null

    // Static method to get singleton instance and handle the event
    static async handle(payload) {
        if (!InstallationEventHandler.instance) {
            InstallationEventHandler.instance = new InstallationEventHandler()
        }
        return await InstallationEventHandler.instance.handleEvent(payload)
    }

    async handleEvent(payload) {
        const { action, installation, repositories = [] } = payload

        console.log(`GitHub installation ${action}:`, installation.id)

        switch (action) {
            case 'created':
                return await this.handleInstallationCreated(installation, repositories)
            case 'deleted':
                return await this.handleInstallationDeleted(installation.id)
            case 'suspend':
                return await this.handleInstallationSuspended(installation)
            case 'unsuspend':
                return await this.handleInstallationUnsuspended(installation)
            case 'new_permissions_accepted':
                return await this.handlePermissionsUpdated(installation)
            default:
                console.log(`Unhandled installation action: ${action}`)
                return { processed: false, handler: 'installation', action }
        }
    }

    async handleInstallationCreated(installation, repositories) {
        try {
            return {
                processed: true,
                handler: 'installation',
                action: 'created',
                installationId: installation.id,
                repositoryCount: repositories.length
            }
        } catch (error) {
            console.error('Error handling installation created:', error)
            throw error
        }
    }

    async handleInstallationDeleted(installation_id) {
        try {
            return await this.supabase.rpc('delete_github_integration', { installation_id_param: installation_id })
        } catch (error) {
            console.error('Error handling installation deleted:', error)
            throw error
        }
    }

    async handleInstallationSuspended(installation) {
        try {
            // Mark integration as inactive
            const { error } = await this.supabase
                .from('github_integrations')
                .update({
                    is_active: false,
                    updated_at: new Date().toISOString()
                })
                .eq('installation_id', installation.id)

            if (error) throw error

            return {
                processed: true,
                handler: 'installation',
                action: 'suspended',
                installationId: installation.id
            }
        } catch (error) {
            console.error('Error handling installation suspended:', error)
            throw error
        }
    }

    async handleInstallationUnsuspended(installation) {
        try {
            // Reactivate integration
            const { error } = await this.supabase
                .from('github_integrations')
                .update({
                    is_active: true,
                    updated_at: new Date().toISOString()
                })
                .eq('installation_id', installation.id)

            if (error) throw error

            return {
                processed: true,
                handler: 'installation',
                action: 'unsuspended',
                installationId: installation.id
            }
        } catch (error) {
            console.error('Error handling installation unsuspended:', error)
            throw error
        }
    }

    async handlePermissionsUpdated(installation) {
        // Handle permission changes
        return {
            processed: true,
            handler: 'installation',
            action: 'permissions_updated',
            installationId: installation.id
        }
    }
}
