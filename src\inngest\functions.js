import { inngest } from './client'
import { getServiceRoleClient } from '@/utils/supabase/server/connect'

// Parent function - orchestrates the entire scan job
export const scanRepositoriesParent = inngest.createFunction(
    { id: 'scan-repositories-parent', concurrency: 5 },
    { event: 'scan/repositories.requested' },
    async ({ event, step }) => {
        try {
            console.log(`🎯 Parent function triggered with event:`, JSON.stringify(event, null, 2))

            const { jobId, repositories } = event.data

            // Validate required data
            if (!jobId) {
                console.error(`❌ Missing jobId in event data`)
                throw new Error('Missing jobId in event data')
            }

            if (!repositories || !Array.isArray(repositories) || repositories.length === 0) {
                console.error(`❌ Invalid repositories data:`, repositories)
                throw new Error('Invalid or empty repositories array')
            }

            console.log(`✅ Event validation passed - jobId: ${jobId}, repositories count: ${repositories.length}`)

        // Step 1: Update job to running
        await step.run('job-start', async () => {
            console.log(`🚀 Parent function started for job: ${jobId}`)
            console.log(`📊 Processing ${repositories.length} repositories`)
            console.log(`📋 Repositories data:`, JSON.stringify(repositories, null, 2))

            const { supabase } = getServiceRoleClient()
            console.log(`🔄 Calling update_job_state with jobId: ${jobId}`)

            const { data, error } = await supabase.rpc('update_job_state', {
                job_id_param: jobId,
                state_param: 'running',
                message_param: 'Scan started successfully'
            })

            console.log(`📊 RPC Response - Data:`, data, `Error:`, error)

            if (error) {
                console.error(`❌ Failed to update job state:`, error)
                throw new Error(`Failed to update job state: ${error.message}`)
            }

            console.log(`✅ Job state updated successfully`)
            return { status: 'running' }
        })

        // Step 2: Fan-out - Send one event per repository
        console.log(`📤 Creating ${repositories.length} child events for job: ${jobId}`)

        const childEvents = repositories.map((repo, index) => {
            console.log(`📋 Repository ${index + 1}:`, {
                repository_id: repo.repository_id,
                full_name: repo.full_name,
                selected_branch: repo.selected_branch
            })

            return {
                name: 'scan/repository.requested',
                data: { jobId, repository: repo }
            }
        })

        await step.sendEvent('send-child-events', childEvents)
        console.log(`✅ Successfully sent ${repositories.length} child events for job: ${jobId}`)

        // Step 3: Fan-in - Wait for all repositories to complete with improved timeout handling
        await step.waitForEvent('all-repos-done', {
            event: 'scan/repository.completed',
            if: `data.jobId == "${jobId}"`,
            count: repositories.length,
            timeout: '2h'
        }).catch(async (error) => {
            // Handle timeout - mark job as failed due to timeout
            const { supabase } = getServiceRoleClient()
            await supabase.rpc('update_job_state', {
                job_id_param: jobId,
                state_param: 'completed',
                is_success_param: false,
                message_param: 'Scan timed out - some repositories may not have completed'
            })
            throw new Error(`Scan job timed out: ${error.message}`)
        })

        // Step 4: Finalize job - check for failures and mark as completed
        await step.run('job-finish', async () => {
            const { supabase } = getServiceRoleClient()

            // Check if any repos failed
            const { data: failedRepos } = await supabase
                .from('scan_job_details')
                .select('id')
                .eq('scan_job_id', jobId)
                .eq('scan_status', 'failed')

            const hasFailures = failedRepos && failedRepos.length > 0

            const { error } = await supabase.rpc('update_job_state', {
                job_id_param: jobId,
                state_param: 'completed',
                is_success_param: !hasFailures,
                message_param: hasFailures
                    ? `Scan completed with ${failedRepos.length} failed repositories`
                    : 'All repositories scanned successfully'
            })

            if (error) throw new Error(`Failed to finalize job: ${error.message}`)
            return { status: 'completed' }
        })

        return {
            jobId,
            repositoryCount: repositories.length,
            status: 'completed'
        }
    }
)

// Child function - scans individual repositories in parallel
export const scanSingleRepository = inngest.createFunction(
    { id: 'scan-single-repository', concurrency: 10 },
    { event: 'scan/repository.requested' },
    async ({ event, step }) => {
        const { jobId, repository } = event.data
        let scanResult = null

        try {
            // Step 1: Update repository to scanning and emit progress
            await step.run('repo-start', async () => {
                const { supabase } = getServiceRoleClient()
                const { error } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'scanning',
                    scan_status_param: 'success'
                })
                if (error) throw new Error(`Failed to update repository state to scanning: ${error.message}`)
                return { repositoryId: repository.repository_id, state: 'scanning' }
            })

            // Step 2: Perform actual scanning with progress updates
            scanResult = await step.run('scan-repo', async () => {
                console.log(`Starting scan for: ${repository.full_name} on branch: ${repository.selected_branch}`)

                // Emit progress for each scanning phase
                const phases = [
                    { name: 'Cloning repository...', delay: 1000 },
                    { name: 'Analyzing code structure...', delay: 1000 },
                    { name: 'Running security checks...', delay: 1000 },
                    { name: 'Generating report...', delay: 1000 }
                ]

                for (let i = 0; i < phases.length; i++) {
                    const phase = phases[i]
                    console.log(`  → ${phase.name}`)
                    await new Promise(resolve => setTimeout(resolve, phase.delay))
                }

                console.log(`  ✓ Completed scan for ${repository.full_name}`)

                return {
                    repository_id: repository.repository_id,
                    status: 'success',
                    message: `Successfully scanned ${repository.full_name}`,
                    branch: repository.selected_branch
                }
            })

            // Step 3: Update repository to completed
            await step.run('repo-complete', async () => {
                const { supabase } = getServiceRoleClient()
                const { error } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'completed',
                    scan_status_param: 'success'
                })
                if (error) throw new Error(`Failed to update repository state to completed: ${error.message}`)
                return { repositoryId: repository.repository_id, state: 'completed' }
            })

        } catch (error) {
            console.error(`Failed to scan ${repository.full_name}:`, error)

            // Step 4: Handle errors - update repository to error state
            await step.run('repo-error', async () => {
                const { supabase } = getServiceRoleClient()
                const { error: updateError } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'completed',
                    scan_status_param: 'failed'
                })
                if (updateError) console.error(`Failed to update repository error state: ${updateError.message}`)
                return { repositoryId: repository.repository_id, state: 'error' }
            })

            scanResult = {
                repository_id: repository.repository_id,
                status: 'error',
                message: error.message,
                branch: repository.selected_branch
            }
        }

        // Step 5: Always emit completion event for parent fan-in
        await step.sendEvent('child-done', {
            name: 'scan/repository.completed',
            data: { jobId, repositoryId: repository.repository_id }
        })

        return scanResult
    }
)
