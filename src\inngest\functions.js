import { inngest } from './client'
import { getServiceRoleClient } from '@/utils/supabase/server/connect'

// Parent function - orchestrates the entire scan job
export const scanRepositoriesParent = inngest.createFunction(
    { id: 'scan-repositories-parent', concurrency: 5 },
    { event: 'scan/repositories.requested' },
    async ({ event, step }) => {
        console.log(`🎯 PARENT FUNCTION STARTED`)
        console.log(`📋 Event:`, JSON.stringify(event, null, 2))

        const { jobId, repositories } = event.data || {}

        console.log(`🔍 JobId: ${jobId}`)
        console.log(`🔍 Repositories count: ${repositories?.length}`)

        // Validate data
        if (!jobId || !repositories || repositories.length === 0) {
            console.error(`❌ Invalid data - jobId: ${jobId}, repositories: ${repositories?.length}`)
            return { error: 'Invalid event data' }
        }

        // Step 1: Update job to running
        await step.run('job-start', async () => {
            console.log(`🚀 Starting job: ${jobId}`)
            const { supabase } = getServiceRoleClient()

            const { data, error } = await supabase.rpc('update_job_state', {
                job_id_param: jobId,
                state_param: 'running',
                message_param: 'Scan started successfully'
            })

            if (error) {
                console.error(`❌ Failed to update job state:`, error)
                throw new Error(`Failed to update job state: ${error.message}`)
            }

            console.log(`✅ Job state updated to running`)
            return { status: 'running' }
        })

        // // Step 2: Fan-out - Send events to child functions
        await step.sendEvent('send-child-events',
            repositories.map(repo => ({
                name: 'scan/repository.requested',
                data: { jobId, repository: repo }
            }))
        )

        console.log(`� Sent ${repositories.length} child events`)

        // Step 3: Wait for all repositories to complete
        await step.waitForEvent('wait-for-completion', {
            event: 'scan/repository.completed',
            if: `data.jobId == "${jobId}"`,
            count: repositories.length,
            timeout: '30m'
        })

        console.log(`✅ All repositories completed`)

        // Step 4: Finalize job
        await step.run('job-finish', async () => {
            console.log(`🏁 Finalizing job: ${jobId}`)
            const { supabase } = getServiceRoleClient()

            const { error } = await supabase.rpc('update_job_state', {
                job_id_param: jobId,
                state_param: 'completed',
                is_success_param: true,
                message_param: 'All repositories scanned successfully'
            })

            if (error) {
                console.error(`❌ Failed to finalize job:`, error)
                throw new Error(`Failed to finalize job: ${error.message}`)
            }

            console.log(`✅ Job finalized successfully`)
            return { status: 'completed' }
        })

        return {
            success: true,
            jobId,
            repositoryCount: repositories.length,
            status: 'completed'
        }
    }
)

// Child function - scans individual repositories in parallel
export const scanSingleRepository = inngest.createFunction(
    { id: 'scan-single-repository', concurrency: 10 },
    { event: 'scan/repository.requested' },
    async ({ event, step }) => {
        const { jobId, repository } = event.data
        let scanResult = null

        try {
            // Step 1: Update repository to scanning and emit progress
            await step.run('repo-start', async () => {
                const { supabase } = getServiceRoleClient()
                const { error } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'scanning',
                    scan_status_param: 'success'
                })
                if (error) throw new Error(`Failed to update repository state to scanning: ${error.message}`)
                return { repositoryId: repository.repository_id, state: 'scanning' }
            })

            // Step 2: Perform actual scanning with progress updates
            scanResult = await step.run('scan-repo', async () => {
                console.log(`Starting scan for: ${repository.full_name} on branch: ${repository.selected_branch}`)

                // Emit progress for each scanning phase
                const phases = [
                    { name: 'Cloning repository...', delay: 1000 },
                    { name: 'Analyzing code structure...', delay: 1000 },
                    { name: 'Running security checks...', delay: 1000 },
                    { name: 'Generating report...', delay: 1000 }
                ]

                for (let i = 0; i < phases.length; i++) {
                    const phase = phases[i]
                    console.log(`  → ${phase.name}`)
                    await new Promise(resolve => setTimeout(resolve, phase.delay))
                }

                console.log(`  ✓ Completed scan for ${repository.full_name}`)

                return {
                    repository_id: repository.repository_id,
                    status: 'success',
                    message: `Successfully scanned ${repository.full_name}`,
                    branch: repository.selected_branch
                }
            })

            // Step 3: Update repository to completed
            await step.run('repo-complete', async () => {
                const { supabase } = getServiceRoleClient()
                const { error } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'completed',
                    scan_status_param: 'success'
                })
                if (error) throw new Error(`Failed to update repository state to completed: ${error.message}`)
                return { repositoryId: repository.repository_id, state: 'completed' }
            })

        } catch (error) {
            console.error(`Failed to scan ${repository.full_name}:`, error)

            // Step 4: Handle errors - update repository to error state
            await step.run('repo-error', async () => {
                const { supabase } = getServiceRoleClient()
                const { error: updateError } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'completed',
                    scan_status_param: 'failed'
                })
                if (updateError) console.error(`Failed to update repository error state: ${updateError.message}`)
                return { repositoryId: repository.repository_id, state: 'error' }
            })

            scanResult = {
                repository_id: repository.repository_id,
                status: 'error',
                message: error.message,
                branch: repository.selected_branch
            }
        }

        // Step 5: Always emit completion event for parent fan-in
        await step.sendEvent('child-done', {
            name: 'scan/repository.completed',
            data: { jobId, repositoryId: repository.repository_id }
        })

        return scanResult
    }
)
