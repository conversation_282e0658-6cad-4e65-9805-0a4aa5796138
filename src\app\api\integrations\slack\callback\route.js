import { auth } from '@clerk/nextjs/server'
import { IntegrationManager } from '@/lib/integrations/integration-manager'
import { NextResponse } from 'next/server'

export async function GET(request) {
    try {
        const { userId } = await auth()
        if (!userId) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
        }

        const { searchParams } = new URL(request.url)
        const code = searchParams.get('code')
        const state = searchParams.get('state')
        const error = searchParams.get('error')

        if (error) {
            console.error('Slack OAuth error:', error)
            return NextResponse.redirect(new URL(`/${userId}/integrations?error=slack_denied`, request.url))
        }

        if (!code) {
            return NextResponse.json({ error: 'Missing authorization code' }, { status: 400 })
        }

        // Verify state parameter matches the logged-in user
        if (state !== userId) {
            return NextResponse.json({ error: 'Invalid state parameter' }, { status: 400 })
        }

        // Exchange code for access token
        const tokenResponse = await fetch('https://slack.com/api/oauth.v2.access', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                client_id: process.env.SLACK_CLIENT_ID,
                client_secret: process.env.SLACK_CLIENT_SECRET,
                code: code,
                redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/integrations/slack/callback`
            })
        })

        const tokenData = await tokenResponse.json()

        if (!tokenData.ok) {
            throw new Error(`Slack OAuth error: ${tokenData.error}`)
        }

        // Get channels list
        const channelsResponse = await fetch('https://slack.com/api/conversations.list', {
            headers: {
                'Authorization': `Bearer ${tokenData.access_token}`,
                'Content-Type': 'application/json'
            }
        })

        const channelsData = await channelsResponse.json()
        const channels = channelsData.ok ? channelsData.channels : []

        await IntegrationManager.connectIntegration(userId, 'slack', {
            team: tokenData.team,
            access_token: tokenData.access_token,
            bot_user_id: tokenData.bot_user_id,
            bot: tokenData.bot,
            scope: tokenData.scope,
            channels: channels
        })

        // Redirect back to integrations page with success
        return NextResponse.redirect(new URL(`/${userId}/integrations?connected=slack`, request.url))
    } catch (error) {
        console.error('Slack callback error:', error)
        return NextResponse.redirect(new URL(`/${userId}/integrations?error=slack_failed`, request.url))
    }
}
