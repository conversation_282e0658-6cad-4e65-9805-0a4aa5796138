import { inngest } from './client'
import { getServiceRoleClient } from '@/utils/supabase/server/connect'

// Parent function - orchestrates the entire scan job
export const scanRepositoriesParent = inngest.createFunction(
    { id: 'scan-repositories-parent', concurrency: 5 },
    { event: 'scan/repositories.requested' },
    async ({ event, step }) => {
        const { jobId, repositories } = event.data

        // Step 1: Update job to running
        await step.run('job-start', async () => {
            console.log(`🚀 Parent function started for job: ${jobId}`)
            console.log(`📊 Processing ${repositories.length} repositories`)

            const { supabase } = getServiceRoleClient()
            const { error } = await supabase.rpc('update_job_state', {
                job_id_param: jobId,
                state_param: 'running',
                message_param: '<PERSON>an started successfully'
            })
            if (error) throw new Error(`Failed to update job state: ${error.message}`)

            // Emit job started event for realtime updates
            // await inngest.send({
            //     name: 'scan/job.progress',
            //     data: {
            //         jobId,
            //         status: 'started',
            //         totalRepositories: repositories.length,
            //         completedRepositories: 0
            //     }
            // })

            return { status: 'running' }
        })

        // Step 2: Fan-out - Send one event per repository
        await step.sendEvent('create-child-events',
            repositories.map(repo => ({
                name: 'scan/repository.requested',
                data: { jobId, repository: repo }
            }))
        )

        // console.log(`📤 Sent ${repositories.length} child events for job: ${jobId}`)

        // Step 3: Fan-in - Wait for all repositories to complete with improved timeout handling
        await step.waitForEvent('all-repos-done', {
            event: 'scan/repository.completed',
            if: `data.jobId == "${jobId}"`,
            count: repositories.length,
            timeout: '2h'
        }).catch(async (error) => {
            // Handle timeout - mark job as failed due to timeout
            const { supabase } = getServiceRoleClient()
            await supabase.rpc('update_job_state', {
                job_id_param: jobId,
                state_param: 'completed',
                is_success_param: false,
                message_param: 'Scan timed out - some repositories may not have completed'
            })
            throw new Error(`Scan job timed out: ${error.message}`)
        })

        // Step 4: Finalize job - check for failures and mark as completed
        await step.run('job-finish', async () => {
            const { supabase } = getServiceRoleClient()

            // Check if any repos failed
            const { data: failedRepos } = await supabase
                .from('scan_job_details')
                .select('id')
                .eq('scan_job_id', jobId)
                .eq('scan_status', 'failed')

            const hasFailures = failedRepos && failedRepos.length > 0

            const { error } = await supabase.rpc('update_job_state', {
                job_id_param: jobId,
                state_param: 'completed',
                is_success_param: !hasFailures,
                message_param: hasFailures
                    ? `Scan completed with ${failedRepos.length} failed repositories`
                    : 'All repositories scanned successfully'
            })

            if (error) throw new Error(`Failed to finalize job: ${error.message}`)

            //     // Emit final job completion event
            //     // await inngest.send({
            //     //     name: 'scan/job.completed',
            //     //     data: {
            //     //         jobId,
            //     //         status: hasFailures ? 'completed_with_errors' : 'completed',
            //     //         totalRepositories: repositories.length,
            //     //         failedRepositories: hasFailures ? failedRepos.length : 0
            //     //     }
            // })

            return { status: 'completed' }
        })

        return {
            jobId,
            repositoryCount: repositories.length,
            status: 'completed'
        }
    }
)

// Child function - scans individual repositories in parallel
export const scanSingleRepository = inngest.createFunction(
    { id: 'scan-single-repository', concurrency: 10 },
    { event: 'scan/repository.requested' },
    async ({ event, step }) => {
        const { jobId, repository } = event.data
        let scanResult = null

        try {
            // Step 1: Update repository to scanning and emit progress
            await step.run('repo-start', async () => {
                const { supabase } = getServiceRoleClient()
                const { error } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'scanning',
                    scan_status_param: 'success'
                })
                if (error) throw new Error(`Failed to update repository state to scanning: ${error.message}`)

                // Emit repository started event for realtime updates
                // await inngest.send({
                //     name: 'scan/repository.progress',
                //     data: {
                //         jobId,
                //         repositoryId: repository.repository_id,
                //         repositoryName: repository.full_name,
                //         status: 'started',
                //         branch: repository.selected_branch
                //     }
                // })

                return { repositoryId: repository.repository_id, state: 'scanning' }
            })

            // Step 2: Perform actual scanning with progress updates
            scanResult = await step.run('scan-repo', async () => {
                console.log(`Starting scan for: ${repository.full_name} on branch: ${repository.selected_branch}`)

                // Emit progress for each scanning phase
                const phases = [
                    { name: 'Cloning repository...', delay: 1000 },
                    { name: 'Analyzing code structure...', delay: 1000 },
                    { name: 'Running security checks...', delay: 1000 },
                    { name: 'Generating report...', delay: 1000 }
                ]

                for (let i = 0; i < phases.length; i++) {
                    const phase = phases[i]
                    console.log(`  → ${phase.name}`)

                    // Emit phase progress
                    // await inngest.send({
                    //     name: 'scan/repository.progress',
                    //     data: {
                    //         jobId,
                    //         repositoryId: repository.repository_id,
                    //         repositoryName: repository.full_name,
                    //         status: 'scanning',
                    //         phase: phase.name,
                    //         progress: Math.round(((i + 1) / phases.length) * 100)
                    //     }
                    // })

                    await new Promise(resolve => setTimeout(resolve, phase.delay))
                }

                console.log(`  ✓ Completed scan for ${repository.full_name}`)

                return {
                    repository_id: repository.repository_id,
                    status: 'success',
                    message: `Successfully scanned ${repository.full_name}`,
                    branch: repository.selected_branch
                }
            })

            // Step 3: Update repository to completed
            await step.run('repo-complete', async () => {
                const { supabase } = getServiceRoleClient()
                const { error } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'completed',
                    scan_status_param: 'success'
                })
                if (error) throw new Error(`Failed to update repository state to completed: ${error.message}`)

                // Emit repository completed event
                // await inngest.send({
                //     name: 'scan/repository.progress',
                //     data: {
                //         jobId,
                //         repositoryId: repository.repository_id,
                //         repositoryName: repository.full_name,
                //         status: 'completed',
                //         progress: 100
                //     }
                // })

                return { repositoryId: repository.repository_id, state: 'completed' }
            })

        } catch (error) {
            console.error(`Failed to scan ${repository.full_name}:`, error)

            // Step 4: Handle errors - update repository to error state
            await step.run('repo-error', async () => {
                const { supabase } = getServiceRoleClient()
                const { error: updateError } = await supabase.rpc('update_individual_repository_state', {
                    repository_id_param: repository.repository_id,
                    job_id_param: jobId,
                    scan_state_param: 'completed',
                    scan_status_param: 'failed'
                })
                if (updateError) console.error(`Failed to update repository error state: ${updateError.message}`)

                // Emit repository error event
                // await inngest.send({
                //     name: 'scan/repository.progress',
                //     data: {
                //         jobId,
                //         repositoryId: repository.repository_id,
                //         repositoryName: repository.full_name,
                //         status: 'error',
                //         error: error.message
                //     }
                // })

                return { repositoryId: repository.repository_id, state: 'error' }
            })

            scanResult = {
                repository_id: repository.repository_id,
                status: 'error',
                message: error.message,
                branch: repository.selected_branch
            }
        }

        // Step 5: Always emit completion event for parent fan-in
        await step.sendEvent('child-done', {
            name: 'scan/repository.completed',
            data: { jobId, repositoryId: repository.repository_id }
        })

        return scanResult
    }
)
