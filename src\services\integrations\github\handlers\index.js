import { InstallationEventHandler } from './installation.js'

export class GitHubWebhookHandler {
    static async handleEvent(eventType, payload) {
        try {
            let result = { processed: false, handler: null }

            switch (eventType) {
                case 'installation':
                case 'installation_repositories':
                    result = await InstallationEventHandler.handle(payload)
                    console.log('Installation event:', result)
                    break

                case 'push':
                    result = { processed: true, handler: 'push', message: 'Push event received' }
                    console.log('Push event:', payload)
                    break

                case 'pull_request':
                    result = { processed: true, handler: 'pull_request', message: 'Pull request event received' }
                    console.log('Pull request event:', payload)
                    break

                case 'issues':
                    result = { processed: true, handler: 'issues', message: 'Issues event received' }
                    console.log('Issues event:', payload)
                    break

                case 'repository':
                    result = { processed: true, handler: 'repository', message: 'Repository event received' }
                    console.log('Repository event:', payload)
                    break

                default:
                    console.log(`Unhandled GitHub event: ${eventType}`)
                    result = { processed: false, handler: 'unknown', message: `Event ${eventType} not handled` }
            }

            return {
                ...result,
                eventType,
                timestamp: new Date().toISOString()
            }

        } catch (error) {
            console.error(`Error handling GitHub ${eventType} event:`, error)
            throw error
        }
    }
}
